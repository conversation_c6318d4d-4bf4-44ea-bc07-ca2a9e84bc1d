import { adminApi } from "@/lib/axios/admin";

export const newsCategoryLabels = {
  introduction: "Giới thiệu",
  promotion: "<PERSON><PERSON>yến mãi",
  news: "Tin tức",
  announcement: "<PERSON>hông báo"
};

export const newsCategoryList = ["news", "announcement", "introduction", "promotion"] as const;
export type NewsCategoryType = typeof newsCategoryList[number];

export type NewsType = {
  admin: {
    full_name: string,
    username: string,
  },
  category: NewsCategoryType,
  content: string,
  created_at: string,
  created_by: number,
  news_id: number,
  title: string
};

export type CreateNewsRequest = {
  title: string; content: string;
  category: NewsCategoryType;
}

export const createNews = async (data: CreateNewsRequest): Promise<NewsType> => {
  return (await adminApi.post('/news', data)).data.data;
}

export type NewsListResponse = {
  news: NewsType[]; total: number;
  page: number; limit: number;
};

export const getNewsList = async (params: {
  page: number,
  limit: number,
  category?: NewsCategoryType | undefined,
  search?: string | undefined
}): Promise<NewsListResponse> => {
  return (await adminApi.get('/news', { params })).data.data;
}

export const deleteNews = async (news_id: number ) => {
  return await adminApi.delete(`/news/${news_id}`);
}
