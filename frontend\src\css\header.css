@import "tailwindcss";
@layer theme, base, components, utilities;
@import "tailwindcss/theme.css" layer(theme);
/* @import "tailwindcss/preflight.css" layer(base); */
@import "tailwindcss/utilities.css" layer(utilities);

@layer components {
  .header {
    @apply
      text-white w-full
      flex items-center justify-between
      transition-all duration-400 ease-in-out;
  }

  .header-floating {
    @apply fixed top-0 left-0 z-[999];
  }

  .header-top {
    @apply h-[100px] bg-transparent shadow-none;
  }

  .header-scrolled {
    @apply h-[60px] bg-gray-950 shadow-md;
  }

  .header-link.poppins-regular {
    @apply cursor-pointer hover:text-red-500 transition-all duration-300;
  }
}