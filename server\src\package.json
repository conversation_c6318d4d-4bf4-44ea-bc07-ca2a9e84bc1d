{"name": "src", "version": "1.0.0", "main": "index.js", "scripts": {"start": "node app.js", "test": "echo \"Error: no test specified\" && exit 1", "serve": "node index.js", "dev": "nodemon app.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "express-oas-generator": "^1.0.48", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "mysql2": "^3.14.0", "sequelize": "^6.37.7", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "devDependencies": {"nodemon": "^3.1.9"}}